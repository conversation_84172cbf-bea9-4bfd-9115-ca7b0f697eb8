{"name": "realtime-chat-ui", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build": "rsbuild build", "dev": "rsbuild dev --open", "preview": "rsbuild preview", "test": "jest"}, "devDependencies": {"@babel/core": "^7.27.3", "@babel/preset-env": "^7.27.2", "@rsbuild/core": "^1.3.15", "@rsbuild/plugin-vue": "^1.0.7", "@testing-library/vue": "^8.1.0", "@types/jest": "^29.5.14", "@types/node": "^22.15.26", "@vue/test-utils": "^2.4.0-alpha.2", "babel-jest": "^26.6.3", "jest": "^26.6.3", "jsdom": "^26.1.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vue": "^3.5.16", "vue-jest": "^5.0.0-alpha.10"}, "dependencies": {"pinia": "^3.0.2", "vue-router": "^4.5.1"}}