{
  "compilerOptions": {
    "lib": [
      "DOM",
      "ES2020"
    ],
    "jsx": "preserve",
    "target": "ES2020",
    "noEmit": true,
    "skipLibCheck": true,
    "jsxImportSource": "vue",
    "useDefineForClassFields": true,
    /* modules */
    "module": "ESNext",
    "isolatedModules": true,
    "resolveJsonModule": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    /* type checking */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    /* path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "src/*"
      ],
      "@/components/*": [
        "src/components/*"
      ],
      "@/views/*": [
        "src/views/*"
      ],
      "@/stores/*": [
        "src/stores/*"
      ],
      "@/services/*": [
        "src/services/*"
      ],
      "@/types/*": [
        "src/types/*"
      ],
      "@/composables/*": [
        "src/composables/*"
      ],
      "@/router/*": [
        "src/router/*"
      ]
    }
  },
  "include": [
    "src"
  ]
}