import { computed } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "@/stores/auth.store";
import type {
  LoginCredentials,
  RegisterCredentials,
  ForgotPasswordRequest,
  ResetPasswordRequest,
} from "@/types/auth.types";

export function useAuth() {
  const authStore = useAuthStore();
  const router = useRouter();

  const user = computed(() => authStore.user);
  const isAuthenticated = computed(() => authStore.isAuthenticated);
  const isLoading = computed(() => authStore.isLoading);
  const error = computed(() => authStore.error);
  const userName = computed(() => authStore.userName);
  const userEmail = computed(() => authStore.userEmail);
  const userAvatar = computed(() => authStore.userAvatar);
  const isOnline = computed(() => authStore.isOnline);

  const tokenExpiryInfo = computed(() => authStore.tokenExpiryInfo);
  const refreshStatus = computed(() => authStore.refreshStatus);

  const login = async (credentials: LoginCredentials) => {
    try {
      await authStore.login(credentials);
      const redirectTo =
        (router.currentRoute.value.query.redirect as string) || "/chat";
      await router.push(redirectTo);
    } catch (error) {
      throw error;
    }
  };

  const register = async (credentials: RegisterCredentials) => {
    try {
      await authStore.register(credentials);
      await router.push("/dashboard");
    } catch (error) {
      throw error;
    }
  };

  const forgotPassword = async (request: ForgotPasswordRequest) => {
    try {
      await authStore.forgotPassword(request);
    } catch (error) {
      throw error;
    }
  };

  const resetPassword = async (request: ResetPasswordRequest) => {
    try {
      await authStore.resetPassword(request);
      await router.push("/login");
    } catch (error) {
      throw error;
    }
  };

  const logout = async () => {
    try {
      await authStore.logout();
      await router.push("/login");
    } catch (error) {
      console.error("Logout error:", error);
      await router.push("/login");
    }
  };

  const clearError = () => {
    authStore.clearError();
  };

  const requireAuth = () => {
    if (!isAuthenticated.value) {
      router.push({
        path: "/login",
        query: { redirect: router.currentRoute.value.fullPath },
      });
      return false;
    }
    return true;
  };

  const requireGuest = () => {
    if (isAuthenticated.value) {
      router.push("/chat");
      return false;
    }
    return true;
  };

  const setOnlineStatus = (online: boolean) => {
    authStore.setOnlineStatus(online);
  };

  const updateLastSeen = () => {
    authStore.updateLastSeen();
  };

  const initializeAuth = async () => {
    await authStore.initializeAuth();
  };

  return {
    // State
    user,
    isAuthenticated,
    isLoading,
    error,
    userName,
    userEmail,
    userAvatar,
    isOnline,
    tokenExpiryInfo,
    refreshStatus,

    // Actions
    login,
    register,
    forgotPassword,
    resetPassword,
    logout,
    clearError,

    // Navigation helpers
    requireAuth,
    requireGuest,

    // Mobile helpers
    setOnlineStatus,
    updateLastSeen,

    // Initialization
    initializeAuth,
  };
}
