<template>
  <div v-if="showDebug" class="token-debug">
    <div class="debug-header">
      <h3>🔍 Token Debug Info</h3>
      <button @click="toggleDebug" class="close-btn">×</button>
    </div>
    
    <div class="debug-content">
      <div class="debug-section">
        <h4>📄 Token Status</h4>
        <div class="debug-item">
          <span class="label">Authenticated:</span>
          <span :class="['value', isAuthenticated ? 'success' : 'error']">
            {{ isAuthenticated ? '✅ Yes' : '❌ No' }}
          </span>
        </div>
        <div class="debug-item">
          <span class="label">Expiry:</span>
          <span class="value">{{ tokenExpiryInfo }}</span>
        </div>
      </div>

      <div class="debug-section">
        <h4>🔄 Refresh Status</h4>
        <div class="debug-item">
          <span class="label">State:</span>
          <span :class="['value', getRefreshStateClass(refreshStatus.state)]">
            {{ getRefreshStateIcon(refreshStatus.state) }} {{ refreshStatus.state }}
          </span>
        </div>
        <div class="debug-item" v-if="refreshStatus.lastRefresh">
          <span class="label">Last Refresh:</span>
          <span class="value">{{ formatDate(refreshStatus.lastRefresh) }}</span>
        </div>
        <div class="debug-item" v-if="refreshStatus.nextRefresh">
          <span class="label">Next Refresh:</span>
          <span class="value">{{ formatDate(refreshStatus.nextRefresh) }}</span>
        </div>
        <div class="debug-item" v-if="refreshStatus.error">
          <span class="label">Error:</span>
          <span class="value error">{{ refreshStatus.error }}</span>
        </div>
      </div>

      <div class="debug-section">
        <h4>👤 User Info</h4>
        <div class="debug-item">
          <span class="label">Name:</span>
          <span class="value">{{ userName || 'Not set' }}</span>
        </div>
        <div class="debug-item">
          <span class="label">Email:</span>
          <span class="value">{{ userEmail || 'Not set' }}</span>
        </div>
      </div>

      <div class="debug-actions">
        <button @click="debugCurrentToken" class="debug-btn">
          🔍 Debug Token
        </button>
        <button @click="forceRefresh" class="debug-btn" :disabled="!isAuthenticated">
          🔄 Force Refresh
        </button>
        <button @click="clearTokens" class="debug-btn danger">
          🗑️ Clear Tokens
        </button>
      </div>
    </div>
  </div>

  <!-- Toggle button -->
  <button v-if="!showDebug" @click="toggleDebug" class="debug-toggle">
    🔍 Debug
  </button>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useAuth } from '@/composables/useAuth';
import { debugToken } from '@/utils/jwt.utils';
import authService from '@/services/auth.service';

// Only show in development
const isDevelopment = process.env.NODE_ENV === 'development';

const showDebug = ref(false);
const { 
  isAuthenticated, 
  userName, 
  userEmail, 
  tokenExpiryInfo, 
  refreshStatus,
  logout 
} = useAuth();

const toggleDebug = () => {
  showDebug.value = !showDebug.value;
};

const getRefreshStateClass = (state: string) => {
  switch (state) {
    case 'success': return 'success';
    case 'failed': 
    case 'expired': return 'error';
    case 'refreshing': return 'warning';
    default: return '';
  }
};

const getRefreshStateIcon = (state: string) => {
  switch (state) {
    case 'idle': return '⏸️';
    case 'refreshing': return '🔄';
    case 'success': return '✅';
    case 'failed': return '❌';
    case 'expired': return '⏰';
    default: return '❓';
  }
};

const formatDate = (date: Date | null) => {
  if (!date) return 'Never';
  return new Date(date).toLocaleString();
};

const debugCurrentToken = () => {
  const token = authService.getStoredToken();
  if (token) {
    debugToken(token, 'Current Token');
  } else {
    console.log('🔍 No token to debug');
  }
};

const forceRefresh = async () => {
  console.log('🔄 Forcing token refresh...');
  // This will be handled by the API service automatically
  try {
    await authService.getCurrentUser(); // This will trigger refresh if needed
  } catch (error) {
    console.error('❌ Force refresh failed:', error);
  }
};

const clearTokens = async () => {
  if (confirm('Are you sure you want to clear all tokens? You will be logged out.')) {
    await logout();
  }
};
</script>

<style scoped>
.token-debug {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 350px;
  max-height: 80vh;
  overflow-y: auto;
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  z-index: 9999;
  backdrop-filter: blur(10px);
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.debug-header h3 {
  margin: 0;
  font-size: 14px;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.debug-content {
  padding: 12px;
}

.debug-section {
  margin-bottom: 16px;
}

.debug-section h4 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #3b82f6;
}

.debug-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  gap: 8px;
}

.label {
  color: rgba(255, 255, 255, 0.7);
  min-width: 80px;
}

.value {
  color: white;
  word-break: break-all;
  text-align: right;
}

.value.success {
  color: #10b981;
}

.value.error {
  color: #ef4444;
}

.value.warning {
  color: #f59e0b;
}

.debug-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.debug-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.debug-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
}

.debug-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.debug-btn.danger {
  border-color: rgba(239, 68, 68, 0.5);
  color: #ef4444;
}

.debug-btn.danger:hover:not(:disabled) {
  background: rgba(239, 68, 68, 0.1);
}

.debug-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 12px;
  z-index: 9998;
  backdrop-filter: blur(10px);
}

.debug-toggle:hover {
  background: rgba(0, 0, 0, 0.9);
}

/* Hide in production */
@media (min-width: 1px) {
  .token-debug,
  .debug-toggle {
    display: none;
  }
}

/* Show only in development */
:global(body[data-env="development"]) .token-debug,
:global(body[data-env="development"]) .debug-toggle {
  display: block;
}

/* Mobile adjustments */
@media (max-width: 768px) {
  .token-debug {
    width: calc(100vw - 40px);
    right: 20px;
    left: 20px;
  }
  
  .debug-toggle {
    bottom: 80px; /* Above mobile navigation */
  }
}
</style>
