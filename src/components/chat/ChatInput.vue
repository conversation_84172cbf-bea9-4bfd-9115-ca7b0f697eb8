<template>
  <form
    class="flex items-center gap-2 px-6 py-4 border-t border-zinc-900 bg-[#202c33]"
    @submit.prevent="send"
  >
    <input
      v-model="input"
      type="text"
      placeholder="Digite uma mensagem"
      class="flex-1 bg-[#111b21] text-white/90 rounded-lg px-4 py-2 focus:outline-none"
      autocomplete="off"
    />
    <button
      type="submit"
      class="bg-blue-600 text-white rounded-lg px-4 py-2 font-medium hover:bg-blue-700 transition"
    >
      <span class="material-symbols-outlined">send</span>
    </button>
  </form>
</template>

<script setup lang="ts">
import { ref } from "vue";

const emit = defineEmits(["send"]);
const input = ref("");

function send() {
  if (input.value.trim()) {
    emit("send", input.value);
    input.value = "";
  }
}
</script>
