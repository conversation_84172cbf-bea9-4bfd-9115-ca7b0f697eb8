<template>
  <form
    class="flex items-center gap-2 px-6 py-4 border-t border-zinc-700 bg-black"
    @submit.prevent="send"
  >
    <button
      type="submit"
      class="text-white rounded-lg px-4 py-2 font-medium hover:bg-zinc-700 transition"
    >
      <span class="material-symbols-outlined">emoji_emotions</span>
    </button>
    <button
      type="submit"
      class="text-white rounded-lg px-4 py-2 font-medium hover:bg-zinc-700 transition"
    >
      <span class="material-symbols-outlined">attach_file</span>
    </button>
    <input
      v-model="input"
      type="text"
      placeholder="Digite uma mensagem"
      class="flex-1 bg-[#111b21] text-white/90 rounded-lg px-4 py-2 focus:outline-none"
      autocomplete="off"
    />
    <button
      type="submit"
      class="text-white rounded-lg px-4 py-2 font-medium hover:bg-zinc-700 transition"
    >
      <span class="material-symbols-outlined">send</span>
    </button>
  </form>
</template>

<script setup lang="ts">
import { ref } from "vue";

const emit = defineEmits(["send"]);
const input = ref("");

function send() {
  if (input.value.trim()) {
    emit("send", input.value);
    input.value = "";
  }
}
</script>
