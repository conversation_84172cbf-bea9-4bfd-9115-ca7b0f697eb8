<template>
  <div id="app">
    <router-view />

    <!-- 🔍 Debug component (only in development) -->
    <TokenDebug v-if="isDevelopment" />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from "vue";
import { useAuth } from "@/composables/useAuth";
import TokenDebug from "@/components/debug/TokenDebug.vue";

// 🔍 Only show debug in development
const isDevelopment = process.env.NODE_ENV === "development";

const { initializeAuth } = useAuth();

onMounted(async () => {
  // Initialize authentication on app start
  await initializeAuth();
});
</script>

<style scoped>
#app {
  width: 100%;
  min-height: 100vh;
}
</style>
