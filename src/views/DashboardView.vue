<template>
  <div class="chat-view">
    <!-- Sidebar com lista de conversas -->
    <div class="chat-sidebar">
      <div class="sidebar-header">
        <div class="user-info">
          <div class="user-avatar">
            <span>ML</span>
          </div>
          <div class="user-details">
            <h3><PERSON><PERSON></h3>
            <span class="status">Online</span>
          </div>
        </div>
        <div class="header-actions">
          <button class="action-btn" title="Nova conversa">
            <svg viewBox="0 0 24 24" width="20" height="20">
              <path
                fill="currentColor"
                d="M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2M6,9H18V11H6V9M14,14H6V12H14V14Z"
              />
            </svg>
          </button>
          <button class="action-btn" title="Menu">
            <svg viewBox="0 0 24 24" width="20" height="20">
              <path
                fill="currentColor"
                d="M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z"
              />
            </svg>
          </button>
        </div>
      </div>

      <div class="search-bar">
        <div class="search-input">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path
              fill="currentColor"
              d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"
            />
          </svg>
          <input type="text" placeholder="Buscar ou iniciar nova conversa" />
        </div>
      </div>

      <div class="conversations-list">
        <div class="conversation-item active">
          <div class="conversation-avatar">
            <span>JD</span>
          </div>
          <div class="conversation-info">
            <div class="conversation-header">
              <h4>João Silva</h4>
              <span class="time">14:30</span>
            </div>
            <div class="conversation-preview">
              <p>Olá! Como você está?</p>
              <div class="unread-count">2</div>
            </div>
          </div>
        </div>

        <div class="conversation-item">
          <div class="conversation-avatar">
            <span>MF</span>
          </div>
          <div class="conversation-info">
            <div class="conversation-header">
              <h4>Maria Fernanda</h4>
              <span class="time">13:45</span>
            </div>
            <div class="conversation-preview">
              <p>Perfeito! Vamos marcar para amanhã então</p>
            </div>
          </div>
        </div>

        <div class="conversation-item">
          <div class="conversation-avatar">
            <span>TC</span>
          </div>
          <div class="conversation-info">
            <div class="conversation-header">
              <h4>Time ChatApp</h4>
              <span class="time">12:15</span>
            </div>
            <div class="conversation-preview">
              <p>Bem-vindo ao ChatApp! 🎉</p>
            </div>
          </div>
        </div>

        <div class="conversation-item">
          <div class="conversation-avatar">
            <span>RS</span>
          </div>
          <div class="conversation-info">
            <div class="conversation-header">
              <h4>Roberto Santos</h4>
              <span class="time">Ontem</span>
            </div>
            <div class="conversation-preview">
              <p>Obrigado pela ajuda!</p>
            </div>
          </div>
        </div>

        <div class="conversation-item">
          <div class="conversation-avatar">
            <span>AF</span>
          </div>
          <div class="conversation-info">
            <div class="conversation-header">
              <h4>Ana Ferreira</h4>
              <span class="time">Ontem</span>
            </div>
            <div class="conversation-preview">
              <p>📷 Foto</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Área principal do chat -->
    <div class="chat-main">
      <div class="chat-header">
        <button @click="goBack" class="back-btn mobile-only">
          <svg viewBox="0 0 24 24" width="20" height="20">
            <path
              fill="currentColor"
              d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"
            />
          </svg>
        </button>

        <div class="contact-info">
          <div class="contact-avatar">
            <span>JD</span>
          </div>
          <div class="contact-details">
            <h3>João Silva</h3>
            <span class="status">online</span>
          </div>
        </div>

        <div class="chat-actions">
          <button class="action-btn" title="Buscar">
            <svg viewBox="0 0 24 24" width="20" height="20">
              <path
                fill="currentColor"
                d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"
              />
            </svg>
          </button>
          <button class="action-btn" title="Mais opções">
            <svg viewBox="0 0 24 24" width="20" height="20">
              <path
                fill="currentColor"
                d="M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z"
              />
            </svg>
          </button>
        </div>
      </div>

      <div class="messages-container">
        <div class="messages-area">
          <div class="date-divider">
            <span>Hoje</span>
          </div>

          <div class="message message-received">
            <div class="message-content">
              <p>Olá! Como você está?</p>
              <span class="message-time">14:30</span>
            </div>
          </div>

          <div class="message message-sent">
            <div class="message-content">
              <p>Oi! Estou bem, obrigado! E você?</p>
              <span class="message-time">14:32 ✓✓</span>
            </div>
          </div>

          <div class="message message-received">
            <div class="message-content">
              <p>Também estou bem! Que bom ver você por aqui 😊</p>
              <span class="message-time">14:33</span>
            </div>
          </div>

          <div class="message message-sent">
            <div class="message-content">
              <p>
                Sim! Este app está ficando muito bom. Parabéns pelo trabalho!
              </p>
              <span class="message-time">14:35 ✓✓</span>
            </div>
          </div>

          <div class="message message-received">
            <div class="message-content">
              <p>
                Obrigado! Ainda estamos desenvolvendo mais funcionalidades. Em
                breve teremos muito mais!
              </p>
              <span class="message-time">14:36</span>
            </div>
          </div>
        </div>
      </div>

      <div class="message-input">
        <button class="attachment-btn" title="Anexar">
          <svg viewBox="0 0 24 24" width="20" height="20">
            <path
              fill="currentColor"
              d="M16.5,6V17.5A4,4 0 0,1 12.5,21.5A4,4 0 0,1 8.5,17.5V5A2.5,2.5 0 0,1 11,2.5A2.5,2.5 0 0,1 13.5,5V15.5A1,1 0 0,1 12.5,16.5A1,1 0 0,1 11.5,15.5V6H10V15.5A2.5,2.5 0 0,0 12.5,18A2.5,2.5 0 0,0 15,15.5V5A4,4 0 0,0 11,1A4,4 0 0,0 7,5V17.5A5.5,5.5 0 0,0 12.5,23A5.5,5.5 0 0,0 18,17.5V6H16.5Z"
            />
          </svg>
        </button>

        <div class="input-container">
          <input
            type="text"
            placeholder="Digite uma mensagem"
            v-model="messageText"
            @keypress.enter="sendMessage"
          />
          <button class="emoji-btn" title="Emoji">
            <svg viewBox="0 0 24 24" width="20" height="20">
              <path
                fill="currentColor"
                d="M12,2C6.47,2 2,6.47 2,12C2,17.53 6.47,22 12,22A10,10 0 0,0 22,12C22,6.47 17.5,2 12,2M12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20M7,13C7.55,13 8,12.55 8,12C8,11.45 7.55,11 7,11C6.45,11 6,11.45 6,12C6,12.55 6.45,13 7,13M17,13C17.55,13 18,12.55 18,12C18,11.45 17.55,11 17,11C16.45,11 16,11.45 16,12C16,12.55 16.45,13 17,13M12,17.23C10.25,17.23 8.71,16.5 7.81,15.42L9.23,14C9.68,14.72 10.75,15.23 12,15.23C13.25,15.23 14.32,14.72 14.77,14L16.19,15.42C15.29,16.5 13.75,17.23 12,17.23Z"
              />
            </svg>
          </button>
        </div>

        <button
          class="send-btn"
          @click="sendMessage"
          :disabled="!messageText.trim()"
        >
          <svg viewBox="0 0 24 24" width="20" height="20">
            <path fill="currentColor" d="M2,21L23,12L2,3V10L17,12L2,14V21Z" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import { useAuth } from "@/composables/useAuth";

const router = useRouter();
const { requireAuth } = useAuth();

// Estado do componente
const messageText = ref("");

onMounted(() => {
  requireAuth();
});

const goBack = () => {
  router.back();
};

const sendMessage = () => {
  if (!messageText.value.trim()) return;

  // Aqui seria implementada a lógica de envio da mensagem
  console.log("Enviando mensagem:", messageText.value);

  // Limpar o input
  messageText.value = "";
};
</script>

<style scoped>
.chat-view {
  min-height: 100vh;
  background: linear-gradient(to bottom, #020917, #101725);
  display: flex;
  overflow: hidden;
}

/* Sidebar */
.chat-sidebar {
  width: 350px;
  background: rgba(255, 255, 255, 0.03);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(10px);
}

.sidebar-header {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
}

.user-details h3 {
  color: white;
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.user-details .status {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.75rem;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.search-bar {
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.search-input {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.5rem;
  padding: 0.75rem;
}

.search-input svg {
  color: rgba(255, 255, 255, 0.5);
  flex-shrink: 0;
}

.search-input input {
  flex: 1;
  background: transparent;
  border: none;
  color: white;
  font-size: 0.875rem;
  outline: none;
}

.search-input input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.conversations-list {
  flex: 1;
  overflow-y: auto;
}

.conversation-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.conversation-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.conversation-item.active {
  background: rgba(255, 255, 255, 0.1);
  border-left: 3px solid #3b82f6;
}

.conversation-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981, #059669);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  flex-shrink: 0;
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.conversation-header h4 {
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-header .time {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.75rem;
  flex-shrink: 0;
}

.conversation-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.conversation-preview p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.75rem;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.unread-count {
  background: #3b82f6;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.5rem;
  border-radius: 1rem;
  min-width: 20px;
  text-align: center;
  flex-shrink: 0;
}

/* Chat Main */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.02);
}

.chat-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-only {
  display: none;
}

.back-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  min-height: 40px;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.contact-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.contact-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981, #059669);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
}

.contact-details h3 {
  color: white;
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.contact-details .status {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.75rem;
}

.chat-actions {
  display: flex;
  gap: 0.5rem;
}

.messages-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.messages-area {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  background-image: radial-gradient(
      circle at 20% 80%,
      rgba(59, 130, 246, 0.05) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(16, 185, 129, 0.05) 0%,
      transparent 50%
    );
}

.date-divider {
  text-align: center;
  margin: 1rem 0;
}

.date-divider span {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
}

.message {
  display: flex;
  max-width: 70%;
  margin-bottom: 0.25rem;
}

.message-received {
  align-self: flex-start;
}

.message-sent {
  align-self: flex-end;
}

.message-content {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  padding: 0.75rem 1rem;
  position: relative;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.message-sent .message-content {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.message-content p {
  color: white;
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
  line-height: 1.4;
  word-wrap: break-word;
}

.message-time {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.75rem;
  float: right;
}

.message-input {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.attachment-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.75rem;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
}

.attachment-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.input-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.5rem;
  padding: 0.75rem 1rem;
}

.input-container input {
  flex: 1;
  background: transparent;
  border: none;
  color: white;
  font-size: 0.875rem;
  outline: none;
  margin-right: 0.5rem;
}

.input-container input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.emoji-btn {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emoji-btn:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.send-btn {
  background: #3b82f6;
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.send-btn:hover:not(:disabled) {
  background: #2563eb;
  transform: scale(1.05);
}

.send-btn:disabled {
  background: rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
  opacity: 0.5;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .chat-view {
    flex-direction: column;
  }

  .chat-sidebar {
    display: none;
  }

  .mobile-only {
    display: flex;
  }

  .chat-main {
    width: 100%;
  }

  .message {
    max-width: 85%;
  }

  .message-input {
    padding: 0.75rem;
  }

  .attachment-btn,
  .send-btn {
    width: 40px;
    height: 40px;
  }
}

/* Scrollbar customization */
.conversations-list::-webkit-scrollbar,
.messages-area::-webkit-scrollbar {
  width: 6px;
}

.conversations-list::-webkit-scrollbar-track,
.messages-area::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.conversations-list::-webkit-scrollbar-thumb,
.messages-area::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.conversations-list::-webkit-scrollbar-thumb:hover,
.messages-area::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}
</style>
