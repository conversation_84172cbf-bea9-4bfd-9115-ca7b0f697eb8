import authService from './auth.service';
import apiService from './api.service';
import { isTokenValid, needsRefresh, getNextRefreshTime } from '@/utils/jwt.utils';
import type { LoginCredentials, RegisterCredentials, AuthResponse, User } from '@/types/auth.types';

// Mock dependencies
jest.mock('./api.service');
jest.mock('@/utils/jwt.utils');
jest.mock('@/config/api.config', () => ({
  BASE_URL: 'http://localhost:5001/api',
  ENDPOINTS: {
    AUTH: {
      LOGIN: '/auth/login',
      SIGNUP: '/auth/signup',
      REFRESH: '/auth/refresh',
      LOGOUT: '/auth/logout',
      FORGOT_PASSWORD: '/auth/forgot-password',
      RESET_PASSWORD: '/auth/reset-password',
      PROFILE: '/auth/profile',
    },
  },
}));

const mockApiService = apiService as jest.Mocked<typeof apiService>;
const mockIsTokenValid = isTokenValid as jest.MockedFunction<typeof isTokenValid>;
const mockNeedsRefresh = needsRefresh as jest.MockedFunction<typeof needsRefresh>;
const mockGetNextRefreshTime = getNextRefreshTime as jest.MockedFunction<typeof getNextRefreshTime>;

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    
    // Default mocks
    mockIsTokenValid.mockReturnValue(true);
    mockNeedsRefresh.mockReturnValue(false);
    mockGetNextRefreshTime.mockReturnValue(Date.now() + 3600000);
  });

  describe('login', () => {
    it('deve fazer login com sucesso', async () => {
      const credentials: LoginCredentials = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const mockResponse = {
        data: {
          user: {
            id: '1',
            email: '<EMAIL>',
            fullName: 'Test User',
            profilePicture: '',
            about: '',
            isOnline: true,
            lastSeen: new Date(),
          },
          token: 'auth-token',
          refreshToken: 'refresh-token',
          expiresIn: 3600,
        },
      };

      mockApiService.post.mockResolvedValue(mockResponse);

      const result = await authService.login(credentials);

      expect(mockApiService.post).toHaveBeenCalledWith('/auth/login', {
        email: credentials.email,
        password: credentials.password,
      });
      expect(result).toEqual({
        user: mockResponse.data.user,
        token: 'auth-token',
        refreshToken: 'refresh-token',
        expiresIn: 3600,
      });
    });

    it('deve adaptar resposta do backend para formato frontend', async () => {
      const credentials: LoginCredentials = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const mockResponse = {
        data: {
          user: {
            id: '1',
            email: '<EMAIL>',
            fullName: 'Test User',
          },
          accessToken: 'access-token', // Backend usa accessToken
          refreshToken: 'refresh-token',
        },
      };

      mockApiService.post.mockResolvedValue(mockResponse);

      const result = await authService.login(credentials);

      expect(result.token).toBe('access-token'); // Deve mapear accessToken para token
      expect(result.expiresIn).toBe(3600); // Deve usar valor padrão
    });

    it('deve lançar erro se login falhar', async () => {
      const credentials: LoginCredentials = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      mockApiService.post.mockRejectedValue(new Error('Invalid credentials'));

      await expect(authService.login(credentials)).rejects.toThrow('Invalid credentials');
    });
  });

  describe('register', () => {
    it('deve registrar usuário com sucesso', async () => {
      const credentials: RegisterCredentials = {
        email: '<EMAIL>',
        password: 'password123',
        fullName: 'Test User',
      };

      const mockResponse = {
        data: {
          user: {
            id: '1',
            email: '<EMAIL>',
            fullName: 'Test User',
            profilePicture: '',
            about: '',
            isOnline: true,
            lastSeen: new Date(),
          },
          token: 'auth-token',
          refreshToken: 'refresh-token',
          expiresIn: 3600,
        },
      };

      mockApiService.post.mockResolvedValue(mockResponse);

      const result = await authService.register(credentials);

      expect(mockApiService.post).toHaveBeenCalledWith('/auth/signup', {
        email: credentials.email,
        fullName: credentials.fullName,
        password: credentials.password,
      });
      expect(result).toEqual({
        user: mockResponse.data.user,
        token: 'auth-token',
        refreshToken: 'refresh-token',
        expiresIn: 3600,
      });
    });
  });

  describe('refreshToken', () => {
    it('deve atualizar token com sucesso', async () => {
      const mockResponse = {
        data: {
          token: 'new-token',
          refreshToken: 'new-refresh-token',
          expiresIn: 3600,
        },
      };

      // Set stored refresh token
      localStorage.setItem('refreshToken', 'stored-refresh-token');
      mockApiService.post.mockResolvedValue(mockResponse);

      const result = await authService.refreshToken();

      expect(mockApiService.post).toHaveBeenCalledWith('/auth/refresh', {
        refreshToken: 'stored-refresh-token',
      });
      expect(result).toEqual({
        token: 'new-token',
        refreshToken: 'new-refresh-token',
        expiresIn: 3600,
      });
    });

    it('deve lançar erro se não houver refresh token', async () => {
      await expect(authService.refreshToken()).rejects.toThrow('No refresh token available');
    });

    it('deve lançar erro se refresh falhar', async () => {
      localStorage.setItem('refreshToken', 'invalid-refresh-token');
      mockApiService.post.mockRejectedValue(new Error('Invalid refresh token'));

      await expect(authService.refreshToken()).rejects.toThrow('Invalid refresh token');
    });
  });

  describe('logout', () => {
    it('deve fazer logout e limpar armazenamento', async () => {
      // Set some stored data
      localStorage.setItem('token', 'auth-token');
      localStorage.setItem('refreshToken', 'refresh-token');
      localStorage.setItem('user', JSON.stringify({ id: '1', name: 'Test' }));

      mockApiService.post.mockResolvedValue({ data: {} });

      await authService.logout();

      expect(mockApiService.post).toHaveBeenCalledWith('/auth/logout');
      expect(localStorage.getItem('token')).toBeNull();
      expect(localStorage.getItem('refreshToken')).toBeNull();
      expect(localStorage.getItem('user')).toBeNull();
    });

    it('deve limpar armazenamento mesmo se API falhar', async () => {
      localStorage.setItem('token', 'auth-token');
      mockApiService.post.mockRejectedValue(new Error('API error'));

      await authService.logout();

      expect(localStorage.getItem('token')).toBeNull();
    });
  });

  describe('getCurrentUser', () => {
    it('deve retornar usuário atual', async () => {
      const mockUser: User = {
        id: '1',
        email: '<EMAIL>',
        fullName: 'Test User',
        profilePicture: '',
        about: '',
        isOnline: true,
        lastSeen: new Date(),
      };

      mockApiService.get.mockResolvedValue({ data: mockUser });

      const result = await authService.getCurrentUser();

      expect(mockApiService.get).toHaveBeenCalledWith('/auth/profile');
      expect(result).toEqual(mockUser);
    });

    it('deve retornar null se falhar', async () => {
      mockApiService.get.mockRejectedValue(new Error('Unauthorized'));

      const result = await authService.getCurrentUser();

      expect(result).toBeNull();
    });
  });

  describe('storage methods', () => {
    it('getStoredToken deve retornar token armazenado', () => {
      localStorage.setItem('token', 'stored-token');

      const result = authService.getStoredToken();

      expect(result).toBe('stored-token');
    });

    it('getStoredToken deve retornar null se não houver token', () => {
      const result = authService.getStoredToken();

      expect(result).toBeNull();
    });

    it('getStoredUser deve retornar usuário armazenado', () => {
      const mockUser = { id: '1', name: 'Test User' };
      localStorage.setItem('user', JSON.stringify(mockUser));

      const result = authService.getStoredUser();

      expect(result).toEqual(mockUser);
    });

    it('getStoredUser deve retornar null se não houver usuário', () => {
      const result = authService.getStoredUser();

      expect(result).toBeNull();
    });

    it('getStoredUser deve retornar null se JSON for inválido', () => {
      localStorage.setItem('user', 'invalid-json');

      const result = authService.getStoredUser();

      expect(result).toBeNull();
    });
  });

  describe('refresh status', () => {
    it('getRefreshStatus deve retornar status de refresh', () => {
      const status = authService.getRefreshStatus();

      expect(status).toEqual({
        isRefreshing: false,
        lastRefresh: null,
        nextRefresh: null,
        refreshCount: 0,
      });
    });
  });

  describe('password reset', () => {
    it('deve enviar solicitação de reset de senha', async () => {
      const request = { email: '<EMAIL>' };
      mockApiService.post.mockResolvedValue({ data: { message: 'Email sent' } });

      await authService.forgotPassword(request);

      expect(mockApiService.post).toHaveBeenCalledWith('/auth/forgot-password', request);
    });

    it('deve resetar senha', async () => {
      const request = { token: 'reset-token', password: 'newpassword' };
      mockApiService.post.mockResolvedValue({ data: { message: 'Password reset' } });

      await authService.resetPassword(request);

      expect(mockApiService.post).toHaveBeenCalledWith('/auth/reset-password', request);
    });
  });

  describe('token validation', () => {
    it('deve validar token usando jwt utils', () => {
      const token = 'test-token';
      mockIsTokenValid.mockReturnValue(true);

      // This would be tested indirectly through other methods
      // since token validation is used internally
      expect(mockIsTokenValid).toBeDefined();
    });
  });
});
