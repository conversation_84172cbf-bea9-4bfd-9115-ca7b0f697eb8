import apiService from "./api.service";
import { isTokenValid, needsRefresh } from "@/utils/jwt.utils";
import type { ApiResponse, ApiError } from "@/types/api.types";

// Mock dependencies
jest.mock("@/utils/jwt.utils");
jest.mock("@/config/api.config", () => ({
  BASE_URL: "http://localhost:5001/api",
  ENDPOINTS: {
    AUTH: {
      LOGIN: "/auth/login",
      SIGNUP: "/auth/signup",
    },
  },
}));

const mockIsTokenValid = isTokenValid as jest.MockedFunction<
  typeof isTokenValid
>;
const mockNeedsRefresh = needsRefresh as jest.MockedFunction<
  typeof needsRefresh
>;

// Mock fetch
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe("ApiService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockFetch.mockClear();

    // Default mocks
    mockIsTokenValid.mockReturnValue(true);
    mockNeedsRefresh.mockReturnValue(false);

    // Mock navigator.onLine
    Object.defineProperty(navigator, "onLine", {
      writable: true,
      value: true,
    });
  });

  describe("initialization", () => {
    it("deve inicializar com URL base padrão", () => {
      expect(apiService).toBeDefined();
    });

    it("deve configurar listeners de rede", () => {
      const addEventListenerSpy = jest.spyOn(window, "addEventListener");

      // Create new instance to trigger constructor
      const { default: ApiService } = require("./api.service");
      new ApiService();

      expect(addEventListenerSpy).toHaveBeenCalledWith(
        "online",
        expect.any(Function)
      );
      expect(addEventListenerSpy).toHaveBeenCalledWith(
        "offline",
        expect.any(Function)
      );

      addEventListenerSpy.mockRestore();
    });
  });

  describe("setAuthToken", () => {
    it("deve definir token de autenticação", () => {
      const token = "test-token";

      apiService.setAuthToken(token);

      // Token should be set internally (we can't directly test private property)
      // But we can test that subsequent requests include the token
      expect(true).toBe(true); // Placeholder assertion
    });
  });

  describe("GET requests", () => {
    it("deve fazer requisição GET com sucesso", async () => {
      const mockResponse = { data: { message: "success" } };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: "OK",
        headers: new Headers({ "content-type": "application/json" }),
        json: jest.fn().mockResolvedValue(mockResponse),
      } as any);

      const result = await apiService.get("/test");

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("/test"),
        expect.objectContaining({
          method: "GET",
          headers: expect.any(Object),
        })
      );
      expect(result).toEqual({
        data: mockResponse,
        status: 200,
        statusText: "OK",
        headers: expect.any(Headers),
      });
    });

    it("deve incluir token de autenticação no header", async () => {
      const token = "test-auth-token";
      apiService.setAuthToken(token);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: "OK",
        headers: new Headers({ "content-type": "application/json" }),
        json: jest.fn().mockResolvedValue({}),
      } as any);

      await apiService.get("/test");

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: token,
          }),
        })
      );
    });

    it("deve lançar erro para resposta não-ok", async () => {
      const errorResponse = { message: "Not found" };
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: "Not Found",
        headers: new Headers({ "content-type": "application/json" }),
        json: jest.fn().mockResolvedValue(errorResponse),
      } as any);

      await expect(apiService.get("/test")).rejects.toThrow();
    });
  });

  describe("POST requests", () => {
    it("deve fazer requisição POST com dados", async () => {
      const postData = { name: "test" };
      const mockResponse = { data: { id: 1, ...postData } };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        statusText: "Created",
        headers: new Headers({ "content-type": "application/json" }),
        json: jest.fn().mockResolvedValue(mockResponse),
      } as any);

      const result = await apiService.post("/test", postData);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("/test"),
        expect.objectContaining({
          method: "POST",
          headers: expect.objectContaining({
            "Content-Type": "application/json",
          }),
          body: JSON.stringify(postData),
        })
      );
      expect(result.data).toEqual(mockResponse);
    });
  });

  describe("PUT requests", () => {
    it("deve fazer requisição PUT", async () => {
      const putData = { name: "updated" };
      const mockResponse = { data: { id: 1, ...putData } };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: "OK",
        headers: new Headers({ "content-type": "application/json" }),
        json: jest.fn().mockResolvedValue(mockResponse),
      } as any);

      const result = await apiService.put("/test/1", putData);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("/test/1"),
        expect.objectContaining({
          method: "PUT",
          body: JSON.stringify(putData),
        })
      );
      expect(result.data).toEqual(mockResponse);
    });
  });

  describe("PATCH requests", () => {
    it("deve fazer requisição PATCH", async () => {
      const patchData = { name: "patched" };
      const mockResponse = { data: { id: 1, ...patchData } };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: "OK",
        headers: new Headers({ "content-type": "application/json" }),
        json: jest.fn().mockResolvedValue(mockResponse),
      } as any);

      const result = await apiService.patch("/test/1", patchData);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("/test/1"),
        expect.objectContaining({
          method: "PATCH",
          body: JSON.stringify(patchData),
        })
      );
      expect(result.data).toEqual(mockResponse);
    });
  });

  describe("DELETE requests", () => {
    it("deve fazer requisição DELETE", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 204,
        statusText: "No Content",
        headers: new Headers(),
        json: jest.fn().mockResolvedValue({}),
      } as any);

      const result = await apiService.delete("/test/1");

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("/test/1"),
        expect.objectContaining({
          method: "DELETE",
        })
      );
      expect(result.status).toBe(204);
    });
  });

  describe("network status", () => {
    it("deve detectar status offline", () => {
      Object.defineProperty(navigator, "onLine", {
        writable: true,
        value: false,
      });

      // Trigger offline event
      const offlineEvent = new Event("offline");
      window.dispatchEvent(offlineEvent);

      // Since we can't directly access private properties, we test behavior
      expect(navigator.onLine).toBe(false);
    });

    it("deve detectar status online", () => {
      Object.defineProperty(navigator, "onLine", {
        writable: true,
        value: true,
      });

      // Trigger online event
      const onlineEvent = new Event("online");
      window.dispatchEvent(onlineEvent);

      expect(navigator.onLine).toBe(true);
    });
  });

  describe("timeout handling", () => {
    it("deve abortar requisição após timeout", async () => {
      jest.useFakeTimers();

      // Mock fetch to never resolve
      mockFetch.mockImplementation(() => new Promise(() => {}));

      const requestPromise = apiService.get("/test");

      // Fast-forward time to trigger timeout
      jest.advanceTimersByTime(10001);

      await expect(requestPromise).rejects.toThrow();

      jest.useRealTimers();
    });
  });

  describe("error handling", () => {
    it("deve tratar erro de rede", async () => {
      mockFetch.mockRejectedValueOnce(new Error("Network error"));

      await expect(apiService.get("/test")).rejects.toThrow("Network error");
    });

    it("deve tratar resposta JSON inválida", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: "OK",
        headers: new Headers({ "content-type": "application/json" }),
        json: jest.fn().mockRejectedValue(new Error("Invalid JSON")),
      } as any);

      await expect(apiService.get("/test")).rejects.toThrow();
    });
  });

  describe("token refresh", () => {
    it("deve tentar refresh de token quando necessário", async () => {
      mockNeedsRefresh.mockReturnValue(true);

      const mockRefreshCallback = jest.fn().mockResolvedValue("new-token");
      apiService.setTokenRefreshCallback(mockRefreshCallback);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: "OK",
        headers: new Headers({ "content-type": "application/json" }),
        json: jest.fn().mockResolvedValue({}),
      } as any);

      await apiService.get("/test");

      expect(mockRefreshCallback).toHaveBeenCalled();
    });
  });

  describe("request queuing (offline)", () => {
    it("deve enfileirar requisições quando offline", async () => {
      Object.defineProperty(navigator, "onLine", {
        writable: true,
        value: false,
      });

      await expect(apiService.post("/test", { data: "test" })).rejects.toThrow(
        "Offline - request queued"
      );
    });

    it("não deve enfileirar requisições GET quando offline", async () => {
      Object.defineProperty(navigator, "onLine", {
        writable: true,
        value: false,
      });

      mockFetch.mockRejectedValueOnce(new Error("Network error"));

      await expect(apiService.get("/test")).rejects.toThrow("Network error");
    });
  });
});
